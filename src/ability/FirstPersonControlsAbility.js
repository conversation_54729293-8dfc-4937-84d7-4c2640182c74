/**
 * 第一人称相机控制能力
 */
export class FirstPersonControlsAbility {
    /**
     * @param {THREE.PerspectiveCamera} ctx.camera The camera to control.
     */
    constructor(ctx, opts = {}) {
        this.ctx = ctx;

        const { THREE } = this.ctx.imports;
        // 用户能否控制
        this.canUserControl = true;
        this.isUserControling = false;
        this.userLastControlTime = 0;
        this.mousedownTime = 0;
        this.mouseupTime = 0;

        // 存储之前的相机数据，用于检测变化
        this._preData = {
            position: new THREE.Vector3(),
            rotation: new THREE.Euler(),
            quaternion: new THREE.Quaternion()
        };
        this._hasFirstRestoreByData = false;

        this.PLAYER_HEIGHT = 1.8;
        // 走路速度
        this.moveSpeed = 15;
        // 跑步速度
        this.fastMoveSpeed = 30;
        // 重力加速度
        this.gravity = 30;
        // 跳跃强度
        this.jumpStrength = 12;
        // 垂直速度
        this.verticalVelocity = 0;
        // 能否跳跃
        this.canJump = true;
        this.raycaster = new THREE.Raycaster();
        this.keysPressed = {};

        // 是否启用碰撞检测
        this.collisionEnabled = opts.collisionEnabled ?? true;
        // 是否启用上升下降
        this.upDownEnabled = opts.upDownEnabled ?? false;

        this.useRightMouseDown = opts.useRightMouseDown ?? false;


        if (opts.playerHeight >= 0) this.PLAYER_HEIGHT = opts.playerHeight;
        else if (ctx.data.player.heightUnit >= 0) this.PLAYER_HEIGHT = ctx.utils.getRealSize(ctx.data.player.heightUnit);
        ctx.utils.log(`玩家身高：${this.PLAYER_HEIGHT}`)

        if (opts.moveSpeed >= 0) this.moveSpeed = opts.moveSpeed;
        else if (ctx.data.player.moveSpeedUnit >= 0) this.moveSpeed = ctx.utils.getRealSize(ctx.data.player.moveSpeedUnit);
        ctx.utils.log(`移动速度：${this.moveSpeed}`)

        if (opts.fastMoveSpeed >= 0) this.fastMoveSpeed = opts.fastMoveSpeed;
        else if (ctx.data.player.fastMoveSpeedUnit >= 0) this.fastMoveSpeed = ctx.utils.getRealSize(ctx.data.player.fastMoveSpeedUnit);
        ctx.utils.log(`跑步速度：${this.fastMoveSpeed}`)

        if (opts.jumpStrength >= 0) this.jumpStrength = opts.jumpStrength;
        else if (ctx.data.player.jumpStrengthUnit >= 0) this.jumpStrength = ctx.utils.getRealSize(ctx.data.player.jumpStrengthUnit);

        if (opts.gravity >= 0) this.gravity = opts.gravity;
        else if (ctx.data.world.gravityUnit >= 0) this.gravity = ctx.utils.getRealSize(ctx.data.world.gravityUnit);
    }

    /**
     * 初始化
     * @param {*} pars 
     */
    init(pars) {
        const self = this;
        // Event Listeners for Keyboard
        document.addEventListener('keydown', (event) => {
            self.keysPressed[event.key.toLowerCase()] = true;
            // 记录用户控制
            self.isUserControling = true;
            self.userLastControlTime = Date.now();
        }, false);
        document.addEventListener('keyup', (event) => {
            self.keysPressed[event.key.toLowerCase()] = false;
            // 记录用户控制
            self.isUserControling = true;
            self.userLastControlTime = Date.now();
        }, false);
    }

    /**
     * 启动
     */
    start(pars) {
        const self = this;
        const { cgWorld } = pars;

        /**
         * 拦截鼠标或触摸事件
         * @param {MouseEvent|TouchEvent} e
         */
        const userControlsAbility = cgWorld.getAbility('user_ctrl');

        let mousedownName = 'mouseleftdown';
        if (self.useRightMouseDown) {
            mousedownName = 'mouserightdown';
        }

        userControlsAbility.setUserEvent('first_person_controls',
            [mousedownName, "mouseup", "mousemove", "touchstart", "touchend", "touchmove"], (e, type) => {

                if (type.endsWith('down') || type.endsWith('start')) {
                    // 记录用户控制
                    self.isUserControling = true;
                    self.userLastControlTime = Date.now();
                    self.mousedownTime = Date.now();
                }

                if (type.endsWith('move')) {
                    if (self.isUserControling && self.mousedownTime > self.mouseupTime) {
                        self.userLastControlTime = Date.now();
                    }
                }

                if (type.endsWith('up') || type.endsWith('end')) {
                    self.mouseupTime = Date.now();
                    self.isUserControling = false;
                    self._touchUserChangeCameraDelay();
                }
            });
    }

    /**
     * 动画更新触发
     * Updates the camera position and state based on user input and physics.
     */
    update(pars) {
        const self = this;
        const { THREE } = this.ctx.imports;

        // 渲染两帧画面之间所经过的时间
        let delta = this.ctx.deltaTime;
        // 获取相机 
        let camera = this.ctx.camera;
        // 获取玩家高度
        let PLAYER_HEIGHT = this.PLAYER_HEIGHT;
        // 获取轨道控制器
        let controls = this._getOrbitControls(camera, this.ctx.renderer, PLAYER_HEIGHT);

        if (controls.enabled !== this.canUserControl) {
            if (this.canUserControl === false) {
                controls.update();
            }
        }
        // 控制OrbitControls的启用状态
        controls.enabled = this.canUserControl;

        // 当前按压的键字典
        let keysPressed = this.keysPressed;
        // 可碰撞对象数组
        let collidableObjects;
        if (this.collisionEnabled) {
            collidableObjects = this.ctx.cgWorld.getCollidableObjects();
        }
        else {
            collidableObjects = [];
        }

        // Movement with collision detection
        // 检测是否按住shift键进行快速移动
        const currentSpeed = keysPressed['shift'] ? this.fastMoveSpeed : this.moveSpeed;
        const moveDistance = currentSpeed * delta;
        const forward = new THREE.Vector3();
        camera.getWorldDirection(forward);
        forward.y = 0;
        forward.normalize();

        const right = new THREE.Vector3();
        right.copy(forward).cross(camera.up).normalize();

        // Store original position for collision rollback
        const originalPosition = camera.position.clone();

        if (keysPressed['w']) {
            if (!this._checkWallCollision(camera.position, forward, moveDistance, collidableObjects)) {
                camera.position.addScaledVector(forward, moveDistance);
            }
        }
        if (keysPressed['s']) {
            if (!this._checkWallCollision(camera.position, forward, -moveDistance, collidableObjects)) {
                camera.position.addScaledVector(forward, -moveDistance);
            }
        }
        if (keysPressed['a']) {
            if (!this._checkWallCollision(camera.position, right, -moveDistance, collidableObjects)) {
                camera.position.addScaledVector(right, -moveDistance);
            }
        }
        if (keysPressed['d']) {
            if (!this._checkWallCollision(camera.position, right, moveDistance, collidableObjects)) {
                camera.position.addScaledVector(right, moveDistance);
            }
        }

        // 上升下降
        if (this.upDownEnabled) {
            // 垂直移动 (Q键下降, E键上升)
            if (keysPressed['q']) {
                camera.position.y -= moveDistance;
            }
            if (keysPressed['e']) {
                camera.position.y += moveDistance;
            }
        }

        // Gravity and Jump
        this._handleGroundCollision(camera, collidableObjects, delta, keysPressed);

        // 防止相机穿墙 - 确保相机与墙面保持最小距离
        this._preventCameraWallPenetration(camera, collidableObjects);

        // Wall collision (simple)
        // camera.position.x = Math.max(-this.roomSize.width/2 + 1, Math.min(this.roomSize.width/2 - 1, camera.position.x));
        // camera.position.z = Math.max(-this.roomSize.depth/2 + 1, Math.min(this.roomSize.depth/2 - 1, camera.position.z));

        // Update controls target
        const lookDirection = new THREE.Vector3();
        camera.getWorldDirection(lookDirection);
        controls.target.copy(camera.position).add(lookDirection);

        // Update controls after all position changes
        controls.update();

        if (this.isUserControling) {
            const gucaeAbility = this.ctx.cgWorld.getAbility('get_user_change_any_event');
            gucaeAbility.touchEvent('user-change-camera', {
                canUserControl: self.canUserControl
            });

            if (Date.now() - this.userLastControlTime > 300) {
                self._touchUserChangeCameraDelay();
            }
        }

        // 恢复相机状态
        if (!this._hasFirstRestoreByData) {
            this._hasFirstRestoreByData = true;

            const ctx = this.ctx;
            const data = ctx.data.camera;

            if (data.position) ctx.camera.position.fromArray(data.position);
            if (data.rotation) ctx.camera.rotation.fromArray(data.rotation);
            if (data.up) ctx.camera.up.fromArray(data.up);
            if (data.zoom) ctx.camera.zoom = data.zoom;
            if (data.fov) ctx.camera.fov = data.fov;
            if (data.near) ctx.camera.near = data.near;
            if (data.far) ctx.camera.far = data.far;
            if (data.aspect) ctx.camera.aspect = data.aspect;

            // 设置玩家初始位置和朝向
            if (this.ctx.enableDevViewport) {
                ctx.utils.log(`恢复相机状态...`)
            }
            else {
                ctx.utils.log(`设置玩家初始位置和朝向...`)
                // 位置
                const pos = this.ctx.utils.getRealSizeList(this.ctx.data.player.startPosUnit);
                if (pos[1] <= 0) pos[1] = 1; // 防止掉落地面以下
                self.ctx.camera.position.set(pos[0], pos[1], pos[2]);
                ctx.utils.log('位置坐标：', this.ctx.data.player.startPosUnit, pos)

                // 朝向
                let finalLookAtPosUnit = [
                    this.ctx.data.player.startPosUnit[0] + this.ctx.data.player.startLookAtPosUnit[0], 
                    this.ctx.data.player.startPosUnit[1] + this.ctx.data.player.startLookAtPosUnit[1], 
                    this.ctx.data.player.startPosUnit[2] + this.ctx.data.player.startLookAtPosUnit[2]];
                const lookAtPos = this.ctx.utils.getRealSizeList(finalLookAtPosUnit);
                self.ctx.camera.lookAt(lookAtPos[0], lookAtPos[1], lookAtPos[2]);
                ctx.utils.log('朝向坐标：', finalLookAtPosUnit)
            }
        }
    }

    active() {
        this.canUserControl = true;
    }

    deactive() {
        this.canUserControl = false;
    }

    /**
     * 获取镜头的当前数据，用于下次加载时恢复（坐标，角度，...）
     */
    getData() {
        const camera = this.ctx.camera;
        const controls = this.orbitControls;

        // 如果有OrbitControls，需要先更新camera的matrix来获取正确的旋转信息
        if (controls) {
            controls.update();
            camera.updateMatrixWorld();
        }

        return {
            position: this.ctx.camera.position.toArray(),
            rotation: this.ctx.camera.rotation.toArray(),
            up: this.ctx.camera.up.toArray(),
            target: controls.target.toArray(),
            zoom: this.ctx.camera.zoom,
            fov: this.ctx.camera.fov,
            near: this.ctx.camera.near,
            far: this.ctx.camera.far,
            aspect: this.ctx.camera.aspect,
        }
    }

    _touchUserChangeCameraDelay() {
        const self = this;
        clearTimeout(this.touchUserChangeCameraDelayThread)
        this.touchUserChangeCameraDelayThread = setTimeout(() => {
            // 同步相机数据
            const data = self.getData();
            for (const i in data) {
                self.ctx.data.camera[i] = data[i];
            }

            if (self.canUserControl) {
                const gucaeAbility = this.ctx.cgWorld.getAbility('get_user_change_any_event');
                gucaeAbility.touchEvent('user-change-camera-delay', {});
            }
        }, 300)
    }

    /**
     * 获取相机的轨道控制器
     * @param {*} camera 
     * @param {*} renderer 
     * @param {*} PLAYER_HEIGHT 
     * @returns 
     */
    _getOrbitControls(camera, renderer, PLAYER_HEIGHT) {
        if (this.orbitControls == null) {
            const { THREE, OrbitControls } = this.ctx.imports;

            const orbitControls = new OrbitControls(camera, renderer.domElement);
            orbitControls.enablePan = false;      // 禁用平移功能
            orbitControls.enableZoom = false;     // 禁用缩放功能
            orbitControls.enableDamping = true;   // 启用阻尼效果，使控制更平滑
            orbitControls.screenSpacePanning = false; // 禁用屏幕空间平移
            orbitControls.minDistance = 1;        // 设置相机到目标的最小距离
            // orbitControls.maxDistance = 50;    // 设置相机到目标的最大距离
            orbitControls.dampingFactor = 0.10;   // 设置阻尼系数，值越大阻尼效果越强（镜头惯性就会越小）
            orbitControls.enableRotate = true;    // 启用旋转功能
            orbitControls.rotateSpeed = 0.5;      // 设置旋转速度
            orbitControls.minPolarAngle = 0;      // 设置垂直旋转的最小角度（弧度）
            orbitControls.maxPolarAngle = Math.PI; // 设置垂直旋转的最大角度（弧度，Math.PI为180度）

            // 根据useRightMouseDown参数配置鼠标按键
            if (this.useRightMouseDown) {
                // 使用右键进行旋转，禁用左键和中键
                orbitControls.mouseButtons = {
                    LEFT: null,                    // 禁用左键
                    MIDDLE: null,                  // 禁用中键
                    RIGHT: THREE.MOUSE.ROTATE      // 右键用于旋转
                };
            } else {
                // 使用默认配置（左键旋转）
                orbitControls.mouseButtons = {
                    LEFT: THREE.MOUSE.ROTATE,      // 左键用于旋转
                    MIDDLE: THREE.MOUSE.DOLLY,     // 中键用于缩放（虽然已禁用）
                    RIGHT: THREE.MOUSE.PAN         // 右键用于平移（虽然已禁用）
                };
            }

            orbitControls.target.set(0, PLAYER_HEIGHT, 0); // Set initial look-at target

            this.orbitControls = orbitControls;
        }

        return this.orbitControls;
    }

    /**
     * 检查水平移动时的墙壁碰撞
     * @param {THREE.Vector3} currentPosition 当前位置
     * @param {THREE.Vector3} direction 移动方向
     * @param {number} distance 移动距离
     * @param {Array} collidableObjects 可碰撞对象数组
     * @returns {boolean} 是否发生碰撞
     */
    _checkWallCollision(currentPosition, direction, distance, collidableObjects) {
        const { THREE } = this.ctx.imports;

        // 玩家碰撞检测参数
        const playerRadius = 0.5; // 玩家碰撞半径
        const checkDistance = Math.abs(distance) + playerRadius; // 检测距离

        // 从玩家胸部高度开始检测（不是脚部）
        const rayOrigin = currentPosition.clone();
        rayOrigin.y -= this.PLAYER_HEIGHT * 0.3; // 从胸部高度检测

        // 标准化移动方向
        const moveDirection = direction.clone().normalize();
        if (distance < 0) {
            moveDirection.multiplyScalar(-1);
        }

        // 多点检测，确保玩家身体不会穿过物体
        const checkPoints = [
            rayOrigin.clone(), // 中心点
            rayOrigin.clone().add(new THREE.Vector3(playerRadius * 0.7, 0, 0)), // 右侧
            rayOrigin.clone().add(new THREE.Vector3(-playerRadius * 0.7, 0, 0)), // 左侧
            rayOrigin.clone().add(new THREE.Vector3(0, 0, playerRadius * 0.7)), // 前侧
            rayOrigin.clone().add(new THREE.Vector3(0, 0, -playerRadius * 0.7)), // 后侧
        ];

        for (let point of checkPoints) {
            this.raycaster.set(point, moveDirection);
            const intersections = this.raycaster.intersectObjects(collidableObjects, true);

            // 如果检测距离内有碰撞，则阻止移动
            if (intersections.length > 0 && intersections[0].distance < checkDistance) {
                return true; // 发生碰撞
            }
        }

        return false; // 无碰撞
    }

    /**
     * 防止相机穿墙 - 确保相机与墙面保持最小距离
     * @param {THREE.PerspectiveCamera} camera 相机对象
     * @param {Array} collidableObjects 可碰撞对象数组
     */
    _preventCameraWallPenetration(camera, collidableObjects) {
        const { THREE } = this.ctx.imports;

        const minDistance = 1; // 相机与墙面的最小距离
        const checkDirections = [
            new THREE.Vector3(1, 0, 0),   // 右
            new THREE.Vector3(-1, 0, 0),  // 左
            new THREE.Vector3(0, 0, 1),   // 前
            new THREE.Vector3(0, 0, -1),  // 后
            new THREE.Vector3(1, 0, 1).normalize(),   // 右前
            new THREE.Vector3(-1, 0, 1).normalize(),  // 左前
            new THREE.Vector3(1, 0, -1).normalize(),  // 右后
            new THREE.Vector3(-1, 0, -1).normalize()  // 左后
        ];

        const rayOrigin = camera.position.clone();

        for (let direction of checkDirections) {
            this.raycaster.set(rayOrigin, direction);
            const intersections = this.raycaster.intersectObjects(collidableObjects, true);

            if (intersections.length > 0) {
                const distance = intersections[0].distance;

                // 如果距离小于最小距离，则将相机推离墙面
                if (distance < minDistance) {
                    const pushBack = minDistance - distance;
                    const pushDirection = direction.clone().multiplyScalar(-pushBack);
                    camera.position.add(pushDirection);
                }
            }
        }
    }

    /**
     * 处理地面碰撞检测和重力
     * @param {THREE.PerspectiveCamera} camera 相机对象
     * @param {Array} collidableObjects 可碰撞对象数组
     * @param {number} delta 时间间隔
     * @param {object} keysPressed 按键状态
     */
    _handleGroundCollision(camera, collidableObjects, delta, keysPressed) {
        const { THREE } = this.ctx.imports;

        // 检测地面碰撞
        this.raycaster.set(camera.position, new THREE.Vector3(0, -1, 0));
        const intersections = this.raycaster.intersectObjects(collidableObjects, false);
        const onGround = intersections.length > 0 && intersections[0].distance < this.PLAYER_HEIGHT + 0.1;

        if (onGround) {
            const groundY = intersections[0].point.y;
            if (this.verticalVelocity <= 0) {
                this.verticalVelocity = 0;
                camera.position.y = groundY + this.PLAYER_HEIGHT;
                this.canJump = true;
            }
        } else {
            this.verticalVelocity -= this.gravity * delta;
            this.canJump = false;
        }

        // 处理跳跃
        if (keysPressed[' '] && this.canJump) {
            this.verticalVelocity = this.jumpStrength;
            this.canJump = false;
        }

        // 应用垂直速度
        camera.position.y += this.verticalVelocity * delta;
    }
}
