import { tjs_getCameraToXZPos } from "../util/tjs-camera-util";
import { tjs_createCirclePlane } from "../util/tjs-geometry-util";

/**
 * 获取用户变动任何事物的事件（用户变动中心）
 */
export class GetUserChangeAnyEventAbility {

    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;

        this._eventListenerMap = {};
    }

    /**
     * 初始化
     * @param {*} pars 
     */
    init(pars) {
    }

    /**
     * 启动
     */
    start() {
    }

    /**
     * 触发事件
     * @param {*} name user-change-camera-delay 用户改变相机事件（延迟上报）
     */
    touchEvent(name, opts = {}) {
        const self = this;

        // 用户改变相机事件（延迟上报）
        if (name === 'user-change-camera-delay') {
            self._requestSaveProject();
        }
        else if (name === 'user-change-camera') {
            self._showCameraToXZPlane();
        }
        else if (name === 'user-select-object') {

        }
        else if (name === 'user-move-object') {
            if (self.ctx.propBarViewModel) {
                self.ctx.propBarViewModel.updateSelectedTab();
            }
        }
        else if (name === 'user-cancel-select-object') {

        }
        else if (name === 'user-add-object') {
            self._requestSaveProject();
        }
        else if (name === 'user-change-object') {
            self._requestSaveProject();
        }
        else if (name === 'user-delete-object') {
            self._requestSaveProject();
        }

        // 反馈给监听者
        for (const i in this._eventListenerMap) {
            const listener = this._eventListenerMap[i];
            if (listener.types.includes(name)) {
                listener.func({
                    ...opts
                }, name);
            }
        }
    }

    addEventListener(name, types, func) {
        if (!this._eventListenerMap[name]) {
            this._eventListenerMap[name] = { types, func };
        }
    }

    /**
     * 请求保存项目
     */
    _requestSaveProject() {
        const self = this;

        if (self.ctx.canAutoSaveProject && self.ctx.data.designer.autoSaveProject && self.ctx.enableDevViewport) {
            clearTimeout(this._toSaveProjectThread);

            this._toSaveProjectThread = setTimeout(() => {
                self.ctx.projectStorageService.save();
            }, 2000);
        }
    }

    /**
     * 显示相机朝向与XZ平面的接触面
     */
    _showCameraToXZPlane() {
        if (this.ctx.enableDevViewport) {
            const pos = tjs_getCameraToXZPos(this.ctx, this.ctx.camera);
            this._buildCameraToXZPlane(pos);
        }
    }

    _buildCameraToXZPlane(pos) {
        const { THREE } = this.ctx.imports;

        if (this.cameraToXZPlane == null) {
            const size = this.ctx.data.designer.groundGrid.eachGridSize;

            const plane = tjs_createCirclePlane(this.ctx, size, null, {
                color: 'silver',
                opacity: 0.5,
            });
            plane.rotation.x = -Math.PI / 2;
            plane.receiveShadow = true;
            plane.userData = {
                canSelect: false,
            };
            this.ctx.scene.add(plane);

            this.cameraToXZPlane = plane;
        }

        const newPosition = new THREE.Vector3();
        newPosition.set(pos[0], pos[1] + 0.5, pos[2]);
        this.cameraToXZPlane.position.copy(newPosition);
    }
}