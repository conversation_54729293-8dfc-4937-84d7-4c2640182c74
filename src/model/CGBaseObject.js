import { newCUIdA } from "../util/id-util.js";

/**
 * 游戏基础对象
 */
export class CGBaseObject {

    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;
        // ID
        if (this.data.id === undefined) this.data.id = newCUIdA();

        this.children = null;
    }

    getId() {
        return this.data.id;
    }


    /*** 父级子级 ***/

    getParentId() {
        return this.data.parentId || '';
    }

    setParentId(val) {
        this.data.parentId = val;
    }

    hasParent() {
        return this.data.parentId != null && this.data.parentId.length > 0;
    }

    async addChild(child) {
        if (this.children == null) this.children = [];
        if (child.getParentId() !== this.getId()) {
            child.setParentId(this.getId());
        }
        this.children.push(child);
    }

    getChildren() {
        return this.children;
    }

    findChildById(id) {
        if (this.children) {
            for (const child of this.children) {
                if (child.getId() === id) {
                    return child;
                }
            }
        }
    }


    /*** ... ***/

    getPropValue(name) {
        return this.data[name];
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps();
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;
            return true;
        }
    }

    static getProps() {
        return [
            { label: 'ID', name: 'id', type: 'String', readonly: true },
        ]
    }


    /*** ... ***/

    getRealSize(sizeUnit) {
        return this.ctx.utils.getRealSize(sizeUnit);
    }

    getRealSizeList(sizeUnitList) {
        return this.ctx.utils.getRealSizeList(sizeUnitList);
    }

    getUnitSize(realSize) {
        return this.ctx.utils.getUnitSize(realSize);
    }

    getUnitSizeList(realSizeList) {
        return this.ctx.utils.getUnitSizeList(realSizeList);
    }

}