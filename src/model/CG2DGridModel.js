import { CGBaseModel } from "./CGBaseModel.js";
/**
 * 2d网格
 */
export class CG2DGridModel extends CGBaseModel {

    constructor(ctx, data = {}) {
        super(ctx, data);

        if (data.canSelect == null) data.canSelect = false;
    }

    build() {
        const self = this;
        const { THREE } = this.ctx.imports;
        // 鼠标移动时的坐标
        this.mouse_2d_pos_move = new THREE.Vector2();

        let eachGridSize = this.data.eachGridSize || this.ctx.data.designer.groundGrid.eachGridSize;
        let totalGridCount = this.data.totalGridCount || this.ctx.data.designer.groundGrid.totalGridCount;
        let colorCenterLine = this.data.colorCenterLine || 'red';
        let colorGrid = this.data.colorGrid || 'silver';

        if (this.gridHelper == null) {
            this.gridHelper = new THREE.GridHelper(eachGridSize * totalGridCount, totalGridCount,
                colorCenterLine, colorGrid);
            this.ctx.scene.add(this.gridHelper);
        }
        if (this.data.pos) {
            this.gridHelper.position.set(this.data.pos[0], this.data.pos[1], this.data.pos[2]);
        }
        this.model = this.gridHelper;

        if (this.userControlsAbility == null) {
            this.userControlsAbility = this.ctx.cgWorld.getAbility('user_ctrl');

            this.userControlsAbility.setUserEvent(`${this.getId()}|CG2DGridModel`,
                ["mousemove"], (e, type) => {
                    // 鼠标移动
                    if (type.startsWith('mousemove')) {
                        // 将鼠标位置转换为标准化设备坐标（NDC）
                        self.mouse_2d_pos_move.x = (e.clientX / window.innerWidth) * 2 - 1;
                        self.mouse_2d_pos_move.y = -(e.clientY / window.innerHeight) * 2 + 1;

                        const newPos = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_move, 0);
                        if (newPos != null) {
                            const gridPos = self.ctx.utils.getDesignGroundGridPosByRealPos(newPos);

                            const gridCenterPos = this.ctx.utils.getDesignGroundRealPosByGridPos(gridPos)
                            gridCenterPos.y = this.gridHelper.position.y;
                            self._showCursorGridPlane(gridCenterPos);
                        }
                    }
                });
        }
    }

    /**
     * 显示鼠标所在网格面
     * @param {*} gPos 
     * @param {*} y 
     */
    _showCursorGridPlane(pos) {
        if (this.cursorHoverPlane == null) {
            const { THREE } = this.ctx.imports;
            const size = this.ctx.data.designer.groundGrid.eachGridSize;
            const geometry = new THREE.PlaneGeometry(size, size);
            const material = new THREE.MeshStandardMaterial({
                color: '#F4F4F4',
                side: THREE.DoubleSide, // 两面都显示
                transparent: true,
                opacity: 0.3,
            });
            const plane = new THREE.Mesh(geometry, material);
            plane.rotation.x = -Math.PI / 2;
            plane.position.y = this.gridHelper.position.y;
            plane.receiveShadow = true;
            plane.userData = {
                cgo: this,
            }
            this.ctx.scene.add(plane);

            this.cursorHoverPlane = plane;
        }

        this.cursorHoverPlane.position.copy(pos);
    }

    /**
     * 计算鼠标在xz面上的坐标，y是高度，已经指定好了
     * @param {*} camera 当前相机
     * @param {THREE.Vector2} mouse_2d_pos_move 鼠标移动坐标
     * @param {*} y 高度
     * @returns {[x, y, z]} 新的坐标
     */
    _calTgtXZNewPos(camera, mouse_2d_pos_move, y) {
        const { THREE } = this.ctx.imports;

        // 创建射线投射器
        const raycaster = new THREE.Raycaster();

        // 从相机位置向鼠标位置发射射线
        raycaster.setFromCamera(mouse_2d_pos_move, camera);

        // 创建一个在指定高度y的水平平面
        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), -y);

        // 计算射线与平面的交点
        const intersectPoint = new THREE.Vector3();
        const intersected = raycaster.ray.intersectPlane(plane, intersectPoint);

        if (intersected) {
            return [intersectPoint.x, y, intersectPoint.z];
        }

        return null;
    }
}