import { tjs_createPlane } from "../util/tjs-geometry-util";
import { CGBaseModel } from "./CGBaseModel";
/**
 * 画框
 */
export class CGPaintingFrameModel extends CGBaseModel {
    constructor(ctx, data) {
        super(ctx, data);

        data.modelLoader = {
            url: `./data/import-model/frame-0`,
        };
    }

    async build() {
        await super.build();

        const imagePlaneWidth = 95;
        const imagePlaneHeight = 125;
        const imagePlane = tjs_createPlane(this.ctx, imagePlaneWidth, imagePlaneHeight, {
            side: 'front', alignmentPointType: 'bottom',
            imageSource: `./data/resource/${this.ctx.data.project.id}/img/${this.data.imageSource}`
        });
        imagePlane.position.y = 27;

        this.model.add(imagePlane);
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '图片源', name: 'imageSource', type: 'String' },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }
}