import { newCUIdA } from "../util/id-util.js";
import {CGNodeContainer} from "./CGNodeContainer";

/**
 * CS Game World Model
 */
export class CGWorldModel {
    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;

        // 所有游戏对象
        this._cgoMap = {};
        // 物理动态对象（mass大于0的）
        this._physDynBodiesMap = {};

        // 可碰撞物体
        this._collidableObjectsMap = {};
        // 可碰撞物体（缓存）
        this._collidableObjectsCache;

        // 能力字典集
        this._abilities = {};

        // 游戏节点容器
        this._nodeContainer = new CGNodeContainer(ctx, {});
    }

    /**
     *
     * @returns {CGNodeContainer}
     */
    getNodeContainer() {
        return this._nodeContainer;
    }

    /**
     *
     * @returns {CGNodeContainer}
     */
    getNodeC() {
        return this._nodeContainer;
    }

    /**
     * 
     * @returns {CGBaseModel[]}
     */
    getObjects() {
        const list = [];
        for (const id in this._cgoMap) {
            list.push(this._cgoMap[id].core);
        }
        return list;
    }

    /**
     * 获取所有可碰撞的对象
     * @returns {THREE.Object3D[]}
     */
    getCollidableObjects() {
        if (this._collidableObjectsCache == null) {
            const list = [];
            for (const id in this._collidableObjectsMap) {
                const collidableObjectCore = this._collidableObjectsMap[id].core;
                list.push(collidableObjectCore.model);
                if (collidableObjectCore.getMoreModels() && collidableObjectCore.getMoreModels().length > 0) {
                    list.push(...collidableObjectCore.getMoreModels());
                }
            }
            this._collidableObjectsCache = list;
        }
        return this._collidableObjectsCache;
    }

    /**
     * 
     * @param {*} pars 
     * @returns {CGBaseModel[]}
     */
    getSelectedObjects(pars = {}) {
        const list = [];
        for (const id in this._cgoMap) {
            const cgo = this._cgoMap[id].core;
            if (cgo.isSelectionModeActive()) {
                if (pars.excludeIds && pars.excludeIds.includes(cgo.getId())) {
                    continue;
                }
                list.push(cgo);
            }
        }
        return list;
    }

    /**
     * 
     * @returns {CGBaseModel}
     */
    getSelectedObject() {
        const list = this.getSelectedObjects();
        if (list.length > 1) {
            list.sort(function (b, a) {
                if (a.activeSelectionModeTime > b.activeSelectionModeTime) {
                    return 1;
                }
                else if (a.activeSelectionModeTime < b.activeSelectionModeTime) {
                    return -1;
                }
                return 0;
            });
            return list[0];
        }
        if (list.length > 0) {
            return list[0];
        }
        return null;
    }

    async buildObjects() {
        for (const id in this._cgoMap) {
            const cgo = this._cgoMap[id].core;
            await cgo.build();
        }
    }

    async addObjectAndBuild(obj) {
        await this.addObject(obj);
        await obj.build();
    }

    async addObject(obj) {
        this._cgoMap[obj.getId()] = { core: obj };

        if (obj.getData().collidableObject) {
            this._collidableObjectsMap[obj.getId()] = { core: obj };
            this._collidableObjectsCache = null;
        }

        if (obj.getData().physBody) {
            if (obj.getData().physBody.mass > 0) {
                this._physDynBodiesMap[obj.getId()] = { core: obj };
            }
        }

        // await obj.build();
        return obj;
    }

    async removeObject(obj) {
        return await this.removeObjectById(obj.getId());
    }

    async removeObjectById(id) {
        let objItem = this._cgoMap[id];
        if (objItem) {
            objItem.core.destroy();
        }
        delete this._cgoMap[id];
        delete this._physDynBodiesMap[id];
        delete this._collidableObjectsMap[id];
        this._collidableObjectsCache = null;
    }

    async startAbilites() {
        for (const name in this._abilities) {
            const ability = this._abilities[name];
            if (ability.start) {
                ability.start({
                    cgWorld: this
                });
            }
        }
    }

    getAbility(name) {
        return this._abilities[name];
    }

    async addAbility(obj, name, pars = {}) {
        if (name == null) {
            name = newCUIdA();
            this.ctx.utils.log('ability name is null, auto create: ', name, obj);
        }
        this._abilities[name] = obj;
        if (obj.init) obj.init(pars);
    }

    /**
     * 刷新世界
     * @param {*} pars 
     */
    update(pars) {
        this.updateObjects(pars);
        this.updateAbilities(pars);
        this.updatePhysDynBodies(pars);
        this.updateNodes(pars);
    }

    updateObjects(pars) {
        for (const id in this._cgoMap) {
            const cgo = this._cgoMap[id].core;
            if (cgo.update) {
                cgo.update(pars);
            }
        }
    }

    updateAbilities(pars) {
        for (const name in this._abilities) {
            const ability = this._abilities[name];
            if (ability.update) {
                ability.update(pars);
            }
        }
    }

    updatePhysDynBodies(pars) {
        for (const prop in this._physDynBodiesMap) {
            const physDynBodyItem = this._physDynBodiesMap[prop];
            // 同步物理体坐标
            const { x, y, z } = physDynBodyItem.core.getPhysBody().position;
            physDynBodyItem.core.setPos(x, y, z)
            physDynBodyItem.core.model.quaternion.copy(physDynBodyItem.core.getPhysBody().quaternion);
        }
    }

    updateNodes(pars) {
        this.getNodeContainer().update(pars);
    }

    resize(pars) {
        this.resizeObjects(pars);
    }

    resizeObjects(pars) {
        for (const id in this._cgoMap) {
            const cgo = this._cgoMap[id].core;
            if (cgo.resize) {
                cgo.resize(pars);
            }
        }
    }
}