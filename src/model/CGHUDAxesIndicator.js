import { tjs_createAxesIndicator } from "../util/tjs-axes-util";
import { tjs_calCameraLookAtPoint } from "../util/tjs-camera-util";
import { CGBaseModel } from "./CGBaseModel";
import {tjs_createCube} from "../util/tjs-geometry-util";
/**
 * HUD顶部坐标指示器（帮助用户知道当前方向坐标朝向）
 */
export class CGHUDAxesIndicator extends CGBaseModel {
    constructor(ctx, data) {
        super(ctx, data);

        this.smallWidth = 200;
        this.smallHeight = 200;
    }

    async build() {
        if (this.model == null) {
            let oCamSize = 15;

            const { THREE, CSS2DRenderer } = this.ctx.imports;

            // 创建坐标指示器（THREE.AxesHelper）
            this.model = tjs_createAxesIndicator(this.ctx, {
                caiAxesSize: this.smallHeight * 0.05,
                arrowLabelDistance: 1,
                caiArrowRadius: 0.3,
            });

            // 添加立方体
            const cube = this._createCube();
            this.model.add(cube);



            this.hudRenderer = new THREE.WebGLRenderer({ antialias: true, logarithmicDepthBuffer: true });
            this.hudRenderer.setSize(this.smallWidth, this.smallHeight);
            this.hudRenderer.setClearColor(0x000000, 0); // required
            this.hudRenderer.domElement.style.position = 'absolute';
            this.hudRenderer.domElement.style.top = '0px';
            this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;

            this.hudRenderer.domElement.style.zIndex = '999';
            document.body.appendChild(this.hudRenderer.domElement);

            this.hudCss2dRenderer = new CSS2DRenderer();
            this.hudCss2dRenderer.setSize(this.smallWidth, this.smallHeight);
            this.hudCss2dRenderer.domElement.style.position = 'absolute';
            this.hudCss2dRenderer.domElement.style.top = '0px';
            this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
            this.hudCss2dRenderer.domElement.style.zIndex = '999';
            document.body.appendChild(this.hudCss2dRenderer.domElement);



            this.hudScene = new THREE.Scene();
            this.hudCamera = new THREE.OrthographicCamera(this.smallWidth / -oCamSize, this.smallWidth / oCamSize,
                this.smallHeight / oCamSize, this.smallHeight / -oCamSize, 0.01, 1000);
            this.hudCamera.position.set(0, 0, 0);
            this.hudScene.add(this.model);

            // 添加环境光源
            this.hudScene.add(new THREE.AmbientLight(0xffffff, 1));
            // 添加方向光源1
            const directionalLight1 = new THREE.DirectionalLight(0xffffff, 5);
            directionalLight1.position.set(55, 35, 55);
            directionalLight1.lookAt(0, 0, 0);
            this.hudScene.add(directionalLight1);
            // 添加方向光源2
            const directionalLight2 = new THREE.DirectionalLight(0xffffff, 5);
            directionalLight2.position.set(-55, -35, -55);
            directionalLight2.lookAt(0, 0, 0);
            this.hudScene.add(directionalLight2);

            // // 添加方向光源辅助
            // const directionalLightHelper = new THREE.DirectionalLightHelper(directionalLight, 5, 0xff0000);
            // this.directionalLightHelper = directionalLightHelper;
            // this.hudScene.add(directionalLightHelper);
        }
        await super.build();

        this.resize();
    }

    resize() {
        this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
        this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
    }

    // 在animate中调用
    update() {
        if (this.model) {
            const { THREE } = this.ctx.imports;
            this.hudCamera.rotation.copy(this.ctx.camera.rotation);

            // 与中心点保持一定距离并看向中心点
            const p0 = tjs_calCameraLookAtPoint(this.ctx, this.hudCamera, 100);
            this.hudCamera.position.sub(p0);
            this.hudCamera.lookAt(this.model.position);

            this.hudRenderer.render(this.hudScene, this.hudCamera);
            this.hudCss2dRenderer.render(this.hudScene, this.hudCamera);
        }
    }

    _createCube() {
        const cubeMesh = tjs_createCube(this.ctx, 3, 3, 3, {
            opacity: 0.8,
            // materialName: 'MeshPhongMaterial',
            more: {
                metalness: 1,
                // roughness: 0.2,
            }
        });
        return cubeMesh;
    }
}