import { tjs_createBulb, tjs_createPointLightHelper } from "../util/tjs-light-util";
import { CGBaseModel } from "./CGBaseModel";
/**
 * 灯泡模型
 */
export class CGBulbModel extends CGBaseModel {
    constructor(ctx, data) {
        super(ctx, data);

        if (data.intensity == null) data.intensity = 3; // 强度
    }

    async build() {
        if (this.model == null) {
            this.model = tjs_createBulb(this.ctx, {});
            this.ctx.scene.add(this.model);
        }

        this.model.getObjectByName('bulbLight').intensity = this.data.intensity;

        // if (this.pointLightHelper) {
        //     this.pointLightHelper.update();
        // }
        // else {
        //     this.pointLightHelper = tjs_createPointLightHelper(this.ctx, this.model.getObjectByName('bulbLight'));

        //     this.pointLightHelper.userData.cgo = this;
        //     this.ctx.scene.add(this.pointLightHelper);
        // }

        await super.build();
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '强度', name: 'intensity', type: 'Number', default: 0, step: 0.5 },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }
}