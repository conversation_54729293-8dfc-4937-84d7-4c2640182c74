import {CGBaseModelNode} from "./CGBaseModelNode";

/**
 * 游戏场景节点
 */
export class CGSceneNode extends CGBaseModelNode{
    constructor(ctx, data) {
        super(ctx, data);
        this.data.type = 'Scene';
    }

    async createModel() {
        const { THREE } = this.ctx.imports;

        return new THREE.Scene();
    }

    isActive() {
        return this.ctx.data.scene.id === this.getId();
    }
}