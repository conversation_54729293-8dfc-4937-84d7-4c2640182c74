import {CGBaseNode} from "./CGBaseNode";

/**
 * 游戏基础模型节点
 */
export class CGBaseModelNode extends CGBaseNode {
    constructor(ctx, data) {
        super(ctx, data);
        this.model = null;
        this.hasAddedToParent = false;
    }

    // /**
    //  * 创建模型
    //  * @returns {Promise<void>}
    //  */
    // async createModel() {
    // }

    /**
     * 构建模型
     * @returns {Promise<void>}
     */
    async buildModel() {
        if (this.model == null && this.createModel) {
            this.model = await this.createModel();
        }
    }

    /**
     * 销毁模型
     * @returns {Promise<void>}
     */
    async destroyModel() {
        const children = this.getChildren();
        if (children) {
            for (const child of children) {
                await child.destroyModel();
            }
        }
        // 删除可见模型
        this._destroyModel(this.model);

        this.model = null;
        this.hasAddedToParent = false;
    }

    _destroyModel(model) {
        // 删除可见模型
        if (model.geometry) {
            model.geometry.dispose();
        }
        if (model.material) {
            if (Array.isArray(model.material)) {
                model.material.forEach(m => {
                    if (m.map) m.map.dispose();
                    m.dispose();
                });
            } else {
                if (model.material.map) model.material.map.dispose();
                model.material.dispose();
            }
        }
        this.ctx.scene.remove(model);
    }
}