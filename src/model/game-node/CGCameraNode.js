import {CGBaseModelNode} from "./CGBaseModelNode";

/**
 * 游戏相机节点
 */
export class CGCameraNode extends CGBaseModelNode{
    constructor(ctx, data) {
        super(ctx, data);
        this.data.type = 'Camera';

        this.cameraHelper = null;
        this.cameraIcon = null;
    }

    async createModel() {
        const { THREE } = this.ctx.imports;

        const group = new THREE.Group();

        // 创建相机
        // const camera = new THREE.OrthographicCamera(-400, 400, 300, -300, 1, 500); // 创建正交相机（2d）
        const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100000); // 创建透视相机（3d）
        camera.position.set(this.data.pos[0], this.data.pos[1], this.data.pos[2]); // 调整相机高度
        camera.lookAt(this.data.lookAtPos[0], this.data.lookAtPos[1], this.data.lookAtPos[2]);
        group.add(camera)

        if (this.ctx.enableDevViewport) {
            // 创建相机助手
            this.cameraHelper = new THREE.CameraHelper(camera);
            group.add(this.cameraHelper);

            // 相机2d图标
            const textureLoader = new THREE.TextureLoader();
            const iconTexture = textureLoader.load("./assets/img/camera.svg"); // 一个小相机图标
            const spriteMaterial = new THREE.SpriteMaterial({ map: iconTexture, depthTest: false });
            const icon = new THREE.Sprite(spriteMaterial);
            icon.scale.set(5, 5, 5);
            icon.position.set(0, 0.7, 0); // 放在相机上方一点
            camera.add(icon);

            this.cameraIcon = icon;
        }

        return group;
    }

    updateModel() {
        if (this.cameraHelper) {
            this.cameraHelper.update();
            this._updateIconScale(this.cameraIcon, this.ctx.camera);
        }
    }

    _updateIconScale(icon, camera, desiredSize = 50) {
        const { THREE } = this.ctx.imports;

        const distance = camera.position.distanceTo(icon.getWorldPosition(new THREE.Vector3()));
        const vFOV = THREE.MathUtils.degToRad(camera.fov); // 垂直视场角
        const heightAtDist = 2 * Math.tan(vFOV / 2) * distance; // 该距离下视锥高度
        const scale = (desiredSize / window.innerHeight) * heightAtDist;
        icon.scale.set(scale, scale, scale);
    }
}