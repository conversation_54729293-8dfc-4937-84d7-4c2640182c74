import {CGBaseObject} from "../CGBaseObject";

/**
 * 游戏基础节点
 */
export class CGBaseNode extends CGBaseObject {
    constructor(ctx, data) {
        super(ctx, data);
        if (this.data.type === undefined) this.data.type = null;
        if (this.data.name === undefined) this.data.name = null;
    }

    getType() {
        return this.data.type;
    }

    getName() {
        return this.data.name;
    }

    /**
     * 构建
     * @returns {Promise<void>}
     */
    async build() {
        if (this.buildModel) {
            await this.buildModel();
        }

        const children = this.getChildren();
        if (children) {
            for (const child of children) {
                await child.build();
                if (this.model && child.model && !child.hasAddedToParent) {
                    this.ctx.utils.log(`${this.getType()}（${this.getId()}） 添加子项 ${child.getType()}（${child.getId()}）`)
                    this.model.add(child.model);
                    child.hasAddedToParent = true;
                }
            }
        }
    }

    // updateModel() {
    // }

    update() {
        if (this.updateModel) this.updateModel();
        const children = this.getChildren();
        if (children) {
            for (const child of children) {
                child.update();
            }
        }
    }

    toData() {
        const data = {
            ...this.data,
            children: [],
        };

        if (this.children && this.children.length > 0) {
            for (const child of this.children) {
                data.children.push(child.toData());
            }
        }

        return data;
    }
}