import { CGBaseModel } from "./CGBaseModel.js";
/**
 * 光源模型
 */
export class CGLightModel extends CGBaseModel {
    constructor(ctx, data) {
        super(ctx, data);
    }

    build() {
        const { THREE } = this.ctx.imports;
        
        if (this.data.ambientLight) {
            // *** 环境光源 ***
            if (this.ambientLight == null) {
                this.ambientLight = new THREE.AmbientLight(0xffffff, this.data.intensity ?? 0.2);
                this.ctx.scene.add(this.ambientLight);
            }
            else {
                this.ambientLight.intensity = this.data.intensity ?? 0.2;
            }
        }
    }
}