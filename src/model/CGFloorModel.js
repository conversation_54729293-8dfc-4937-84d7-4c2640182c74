import { CGBaseModel } from "./CGBaseModel";
/**
 * 地板模型
 */
export class CGFloorModel extends CGBaseModel {
    constructor(ctx, data) {
        super(ctx, data);

        // 长度
        if (data.sizeXUnit == null) data.sizeXUnit = 10;
        // 厚度
        if (data.sizeYUnit == null) data.sizeYUnit = 0.2;
        // 宽度
        if (data.sizeZUnit == null) data.sizeZUnit = 6;

        data.modelCreator = {
            type: 'cube',
        };

        data.collidableObject = {};
    }

    async build() {
        await super.build();
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps({ onlySelf: true });
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;
            this.destroy();
            await this.build();
            return true;
        }
        else {
            return await super.setPropValue(name, value);
        }
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },
            { label: '厚度（米）', name: 'sizeYUnit', type: 'Number' },
            { label: '宽度（米）', name: 'sizeZUnit', type: 'Number' },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }
}