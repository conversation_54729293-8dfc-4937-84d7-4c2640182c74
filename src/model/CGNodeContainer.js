import {CGBaseObject} from "./CGBaseObject";

/**
 * 游戏节点容器
 */
export class CGNodeContainer extends CGBaseObject{
    constructor(ctx, data) {
        super(ctx, data);

        // 游戏节点字典
        this._cgNodeMap = {};
        // 游戏根节点字典
        this._cgRootNodeMap = {};
        // 未找到父级节点的节点字典
        this._notFindParentNodeMap = {};
    }

    async build() {
        for (const id in this._cgRootNodeMap) {
            const node = this._cgRootNodeMap[id];
            await node.build();
        }
    }

    update(pars) {
        for (const id in this._cgRootNodeMap) {
            const node = this._cgRootNodeMap[id];
            if (node.getType() === this.ctx.constants.SceneNodeTypeName && node.isActive() === false) {
            }
            else if (node.update) {
                node.update(pars);
            }
        }
    }

    resize(pars) {

    }

    /**
     * 添加节点
     * @param node
     */
    async add(node) {
        if (this._cgNodeMap[node.getId()]) {
            this.ctx.utils.log(`重复添加节点 -> ${node.getId()}`);
        }
        else {
            this._cgNodeMap[node.getId()] = node;
            // 挂钩父级
            if (node.hasParent()) {
                if (this._cgNodeMap[node.getParentId()]) {
                    const parentNode = this._cgNodeMap[node.getParentId()];
                    const childNode = node;
                    await parentNode.addChild(childNode);
                }
                else {
                    this._notFindParentNodeMap[node.getId()] = node;
                    this.ctx.utils.log(`未找到父级节点 -> 父级ID：${node.getParentId()}`);
                }
            }
            else {
                this._cgRootNodeMap[node.getId()] = node;
            }
        }

        this.onChange();
    }

    findById(id) {
        return this._cgNodeMap[id];
    }

    findByType(type) {
        for (const id in this._cgNodeMap) {
            const node = this._cgNodeMap[id];
            if (node.getType() === type) {
                return node;
            }
        }
    }

    toData() {
        const list = [];
        for (const id in this._cgRootNodeMap) {
            const node = this._cgRootNodeMap[id];
            list.push(node.toData());
        }
        return list;
    }

    async addDataList(nodeDataList) {
        for (const nodeData of nodeDataList) {
            await this._addDataList_addNode(nodeData);
        }

        this.onChange();
    }

    async _addDataList_addNode(nodeData) {
        const node = new this.ctx.nodeTypes[`CG${nodeData.type}Node`](this.ctx, nodeData);
        await this.add(node);

        if (nodeData.children) {
            for (const childNodeData of nodeData.children) {
                await this._addDataList_addNode(childNodeData);
            }
        }
    }

    onChange() {
        this.ctx.utils.log(`节点容器发生更变`)
        if (this.ctx.sandboxConnection) {
            this.ctx.sandboxConnection.touchNodeContainerChange({
                ctx: this.ctx,
                nodeContainer: this,
            });
        }
    }
}