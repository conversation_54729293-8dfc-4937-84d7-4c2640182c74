/**
 * 物理体创建者
 */
export class PhysBodyCreatorPart {
    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;
    }

    build(pars) {
        const self = this;
        const { THREE, CANNON } = this.ctx.imports;
        const { debug, scale, box, mass, children, restitution, friction } = pars;

        const physBody = new CANNON.Body({ mass: mass || 0 }); // 静态物体
        this.ctx.physWorld.addBody(physBody);

        const size = box.getSize(new THREE.Vector3());

        let boxSizeX = size.x * scale;
        let boxSizeY = size.y * scale;
        let boxsizeZ = size.z * scale;

        // 根据组合方式创建物理体
        if (children) {
            if (debug) self.ctx.utils.log('创建物理体b')
            for (const i in children) {
                const child = children[i];

                let shape;
                // 球型
                if (child.type === 'sphere') {
                    const childScale = child.scale || 1;
                    let childShapeSizeX = boxSizeX * childScale;
                    let childShapeSizeY = boxSizeY * childScale;
                    let childShapeSizeZ = boxsizeZ * childScale;
                    let posY = 0;
                    if (child.posYType === 1) {
                        posY = childShapeSizeY / 2 * (child.offsetYScale || 1);
                    }

                    const radius = Math.min(childShapeSizeX, childShapeSizeZ) / 2;
                    shape = new CANNON.Sphere(radius);
                    physBody.addShape(shape, new CANNON.Vec3(0, posY, 0));
                }
                // 方形
                else {
                    const childScale = child.scale || 1;
                    let childShapeSizeX = boxSizeX * childScale;
                    let childShapeSizeY = boxSizeY * childScale;
                    let childShapeSizeZ = boxsizeZ * childScale;
                    let posY = 0;
                    if (child.posYType === 1) {
                        posY = childShapeSizeY / 2 * (child.offsetYScale || 1);
                    }

                    shape = new CANNON.Box(new CANNON.Vec3(childShapeSizeX / 2, childShapeSizeY / 2, childShapeSizeZ / 2));
                    physBody.addShape(shape, new CANNON.Vec3(0, posY, 0));
                }
            }
        }
        // 根据边界盒子创建物理体
        else {
            if (debug) self.ctx.utils.log('创建物理体a')
            // 创建形状
            const shape0 = new CANNON.Box(new CANNON.Vec3(boxSizeX / 2, boxSizeY / 2, boxsizeZ / 2));
            physBody.addShape(shape0, new CANNON.Vec3(0, 0, 0));
        }

        // 设置树的物理材质
        physBody.material = new CANNON.Material();
        physBody.material.restitution = restitution || 0.3; // 弹性
        physBody.material.friction = friction || 0.8; // 摩擦力

        return physBody;
    }
}