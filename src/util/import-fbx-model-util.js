

/**
 * 导入fbx格式的模型
 * @param {*} dirUrl 模型存放目录（包含base.fbx、shaded.png、texture_diffuse.png、texture_normal.png、texture_metallic.png、texture_roughness.png、texture_pbr.png）

 * @param {*} opts 
 * @returns 
 */
export async function importFBXModel(ctx, dirUrl, opts = {}) {
    const { THREE, FBXLoader } = ctx.imports;
    let baseFileName = opts.baseFileName || 'base_basic_pbr.fbx'; // base.fbx | base_basic_pbr.fbx | base_basic_shaded.fbx
    let baseFilePath = `${dirUrl}/${baseFileName}`;

    const manager = new THREE.LoadingManager();
    const loader = new FBXLoader(manager);

    ctx.utils.log(`加载模型：${baseFilePath}`)

    const model = await new Promise((resolve) => {
        loader.load(
            baseFilePath, // 替换为你的模型文件路径
            function (object) {
                if (opts.debug) ctx.utils.log('模型加载成功');

                // 手动加载纹理
                const textureLoader = new THREE.TextureLoader();
                
                // 加载所有纹理
                const textures = {
                    shaded: textureLoader.load(`${dirUrl}/shaded.png`),
                    diffuse: textureLoader.load(`${dirUrl}/texture_diffuse.png`),
                    normal: textureLoader.load(`${dirUrl}/texture_normal.png`),
                    metallic: textureLoader.load(`${dirUrl}/texture_metallic.png`),
                    roughness: textureLoader.load(`${dirUrl}/texture_roughness.png`),
                    pbr: textureLoader.load(`${dirUrl}/texture_pbr.png`)
                };
                
                // 设置纹理加载回调
                Object.keys(textures).forEach(key => {
                    textures[key].onLoad = function() {
                        if (opts.debug) ctx.utils.log(`${key}纹理加载成功`);
                    };
                    textures[key].onError = function(error) {
                        if (opts.debug) ctx.utils.error(`${key}纹理加载失败：`, error);
                    };
                });

                // 检查并修复材质
                object.traverse((child) => {
                    // 注意：如果导入的模型里已经做了空气墙，可以通过命令筛选的方式来获取这些空气墙对象，例：collider_xxx
                    // ctx.utils.log(child.name, child.isMesh)

                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                        if (opts.debug) ctx.utils.log('网格名称:', child.name);
                        if (opts.debug) ctx.utils.log('材质信息:', child.material);

                        const applyTextures = (material) => {
                            // 应用基础纹理
                            if (!material.map) {
                                material.map = textures.diffuse || textures.shaded;
                            }
                            
                            // 应用法线贴图
                            if (textures.normal) {
                                material.normalMap = textures.normal;
                            }
                            
                            // 应用金属度贴图
                            if (textures.metallic) {
                                material.metalnessMap = textures.metallic;
                                material.metalness = 1.0; // 设置金属度基础值
                            }
                            
                            // 应用粗糙度贴图
                            if (textures.roughness) {
                                material.roughnessMap = textures.roughness;
                                material.roughness = 1.0; // 设置粗糙度基础值
                            }
                            
                            // 如果有PBR贴图，可以用作环境遮蔽贴图
                            if (textures.pbr) {
                                material.aoMap = textures.pbr;
                                material.aoMapIntensity = 1.0;
                            }
                            
                            material.needsUpdate = true;
                        };

                        if (child.material) {
                            // 如果是数组材质
                            if (Array.isArray(child.material)) {
                                child.material.forEach((mat) => {
                                    applyTextures(mat);
                                });
                            } else {
                                // 单个材质
                                applyTextures(child.material);
                            }
                        }
                    }
                });

                resolve(object);
            },
            function (xhr) {
                // 加载进度回调（可选）
                if (opts.debug) ctx.utils.log((xhr.loaded / xhr.total * 100) + '% loaded');
            },
            function (error) {
                // 加载错误回调（可选）
                if (opts.debug) ctx.utils.error('An error happened', error);
            }
        );
    });

    return {
        model,
    };
}

/**
 * 导入fbx格式的模型（简单）
 * @param {*} dirUrl 模型存放目录（包含base.fbx、shaded.png）
 * @param {*} opts 
 * @returns 
 */
export async function importFBXModel_0(ctx, dirUrl, opts = {}) {
    const { THREE, FBXLoader } = ctx.imports;

    const manager = new THREE.LoadingManager();
    const loader = new FBXLoader(manager);
    const model = await new Promise((resolve) => {
        loader.load(
            `${dirUrl}/base.fbx`, // 替换为你的模型文件路径
            function (object) {
                if (opts.debug) ctx.utils.log('模型加载成功');

                // 手动加载纹理
                const textureLoader = new THREE.TextureLoader();
                const texture = textureLoader.load(`${dirUrl}/shaded.png`,
                    function (texture) {
                        if (opts.debug) ctx.utils.log('纹理加载成功。', texture);
                    },
                    function (progress) {
                        if (opts.debug) ctx.utils.log('纹理加载进度：', progress);
                    },
                    function (error) {
                        if (opts.debug) ctx.utils.error('纹理加载失败：', error);
                    }
                );

                // 检查并修复材质
                object.traverse((child) => {
                    // 注意：如果导入的模型里已经做了空气墙，可以通过命令筛选的方式来获取这些空气墙对象，例：collider_xxx
                    // ctx.utils.log(child.name, child.isMesh)

                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                        if (opts.debug) ctx.utils.log('网格名称:', child.name);
                        if (opts.debug) ctx.utils.log('材质信息:', child.material);

                        if (child.material) {
                            // 如果是数组材质
                            if (Array.isArray(child.material)) {
                                child.material.forEach((mat, index) => {
                                    if (opts.debug) ctx.utils.log(`材质${index}:`, mat);
                                    if (!mat.map) {
                                        mat.map = texture;
                                        mat.needsUpdate = true;
                                    }
                                });
                            } else {
                                // 单个材质
                                if (!child.material.map) {
                                    child.material.map = texture;
                                    child.material.needsUpdate = true;
                                    if (opts.debug) ctx.utils.log('已应用纹理到材质');
                                }
                            }
                        }
                    }
                });

                resolve(object);
            },
            function (xhr) {
                // 加载进度回调（可选）
                if (opts.debug) ctx.utils.log((xhr.loaded / xhr.total * 100) + '% loaded');
            },
            function (error) {
                // 加载错误回调（可选）
                if (opts.debug) ctx.utils.error('An error happened', error);
            }
        );
    });

    return {
        model,
    };
}
