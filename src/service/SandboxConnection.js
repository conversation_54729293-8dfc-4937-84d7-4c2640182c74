import {CGSkyModel} from "../model/CGSkyModel";
import {FirstPersonControlsAbility} from "../ability/FirstPersonControlsAbility";
import {UserControlsAbility} from "../ability/UserControlsAbility";
import {GetUserChangeAnyEventAbility} from "../ability/GetUserChangeAnyEventAbility";
import {CG2DGridModel} from "../model/CG2DGridModel";
import {CGBaseModel} from "../model/CGBaseModel";

/**
 * 沙盒连接，用于给编辑器访问沙盒
 */
export class SandboxConnection {

    constructor(ctx) {
        this.ctx = ctx;

        this._devTipEventListeners = [];
        this._nodeContainerChangeEventListeners = [];
    }

    getCtx() {
        return this.ctx;
    }

    /**
     * 初始化
     * @param data
     * @returns {Promise<void>}
     */
    async init(data = {}) {
        if (data.projectId != null) {
            this.ctx.data.project.id = data.projectId;
        }
        if (data.projectName != null) {
            this.ctx.data.project.name = data.projectName;
        }

        if (data.showFPS != null) {
            this.ctx.showFPS = data.showFPS;
            this.ctx.utils.showFPSMonitor();
        }
        if (data.showMemory != null) {
            this.ctx.showMemory = data.showMemory;
            this.ctx.utils.showMemoryMonitor();
        }

        if (data.player) {
            if (data.player.moveSpeedUnit != null) {
                this.ctx.data.player.moveSpeedUnit = data.player.moveSpeedUnit;
            }
            if (data.player.fastMoveSpeedUnit != null) {
                this.ctx.data.player.fastMoveSpeedUnit = data.player.fastMoveSpeedUnit;
            }
        }

        return {}
    }

    /**
     * 开启天空盒子
     * @param data
     * @returns {Promise<void>}
     */
    async openSkyBox(data = {}) {
        await this.ctx.cgWorld.addObject(new CGSkyModel(this.ctx, data));
    }

    /**
     * 开启相机控制
     * @param data
     * @returns {Promise<void>}
     */
    async openCameraControls(data = {}) {
        await this.ctx.cgWorld.addAbility(new FirstPersonControlsAbility(this.ctx, data), 'first_person_controls')
    }

    /**
     * 开启网格
     * @param data
     * @returns {Promise<void>}
     */
    async openGrid(data = {}) {
        this.ctx.data.designer.groundGrid.eachGridSize = this.ctx.utils.getRealSize(data.eachGridSizeUnit);
        this.ctx.data.designer.groundGrid.totalGridCount = data.totalGridCount;
        await this.ctx.cgWorld.addObject(new CG2DGridModel(this.ctx, data));
    }

    /**
     * 开启中心坐标指示器
     * @param data
     * @returns {Promise<void>}
     */
    async openCenterAxesIndicator(data = {}) {
        await this.ctx.cgWorld.addObject(new CGBaseModel(this.ctx, {
            helper: {
                type: 'center-axes-indicator'
            }
        }));
    }

    /**
     * 添加开发者提示事件监听
     * @param func
     */
    addDevTipEventListener(func) {
        this._devTipEventListeners.push({
            func,
        });
    }

    addNodeContainerChangeEventListener(func) {
        this._nodeContainerChangeEventListeners.push({
            func,
        });
    }

    async build() {
        await this.ctx.cgWorld.addAbility(new UserControlsAbility(this.ctx, {}), 'user_ctrl');
        await this.ctx.cgWorld.addAbility(new GetUserChangeAnyEventAbility(this.ctx, {}), 'get_user_change_any_event');

        await this.ctx.cgWorld.startAbilites();
        await this.ctx.cgWorld.buildObjects();
    }

    isReady() {
        return this.ctx.isReady;
    }

    isLoadProjectDataDone() {
        return this.ctx.data.project.loadDataDoneState && this.ctx.data.project.loadDataDoneState.length > 0;
    }

    /**
     * 输出开发者提示，例如：项目保存成功
     * @param msg
     */
    showDevTip(msg) {
        this._devTipEventListeners.forEach((n) => {
            n.func(msg);
        })
    }

    touchNodeContainerChange() {
        this._nodeContainerChangeEventListeners.forEach((n) => {
            n.func();
        })
    }
}