/**
 * 用户控制器（提供用户操作时间）
 */
export class UserControllerService {

    constructor(ctx, cfg = {}) {
        this.ctx = ctx;
        this.cfg = cfg;

        if (this.cfg.debug == null) this.cfg.debug = false;

        this._userEventMap = {};
        this._mouseDownTime = null;

        const self = this;

        // 定义事件【pc端】
        self._mousedown = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】mousedown -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("mousedown") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._mouseup = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】mouseup -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("mouseup") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._mousemove = function (e) {
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("mousemove") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._keydown = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】keydown -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("keydown") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._keyup = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】keyup -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("keyup") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        // 定义事件【移动端】
        self._touchstart = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】touchstart -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchstart") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._touchmove = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】touchmove -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchmove") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }

        self._touchend = function (e) {
            if (self.cfg.debug) {
                self.ctx.utils.log(`【调试】touchend -> ${e}`);
                self.ctx.utils.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchend") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e);
                }
            }
        }
    }

    /**
     * 注册用户事件
     * @param key {string}
     * @param types {string[]} 例：["mousedown", "mouseup", "mousemove"]
     * @param func {function}
     */
    setUserEvent(key, types, func) {
        if (this.cfg.debug) {
            this.ctx.utils.log(`【调试】注册用户事件 -> ${key}`);
        }

        if (this._userEventMap[key] !== undefined) {
            throw new Error(`重复注册用户事件 -> ${key}`);
        }
        else {
            this._userEventMap[key] = {
                types: types,
                func: func
            };
        }
    }

    /**
     * 删除用户事件
     * @param key
     */
    deleteUserEvent(key) {
        if (this.cfg.debug) {
            this.ctx.utils.log(`【调试】删除用户事件 -> ${key}`);
        }
        delete this._userEventMap[key];
    }

    /**
     * 激活事件拦截
     */
    start() {
        // 拦截事件【pc端】
        document.addEventListener(
            "mousedown",
            this._mousedown,
            false
        );
        document.addEventListener(
            "mouseup",
            this._mouseup,
            false
        );
        document.addEventListener(
            "mousemove",
            this._mousemove,
            false
        );
        document.addEventListener(
            "keydown",
            this._keydown,
            false
        );
        document.addEventListener(
            "keyup",
            this._keyup,
            false
        );
        // 拦截事件【移动端】
        document.addEventListener(
            "touchstart",
            this._touchstart,
            false
        );
        document.addEventListener(
            "touchmove",
            this._touchmove,
            false
        );
        document.addEventListener(
            "touchend",
            this._touchend,
            false
        );
    }

    /**
     * 取消事件拦截
     */
    stop() {
        // 取消事件拦截【pc端】
        document.removeEventListener(
            "mousedown",
            this._mousedown,
            false
        );
        document.removeEventListener(
            "mouseup",
            this._mouseup,
            false
        );
        document.removeEventListener(
            "mousemove",
            this._mousemove,
            false
        );
        document.removeEventListener(
            "keydown",
            this._keydown,
            false
        );
        document.removeEventListener(
            "keyup",
            this._keyup,
            false
        );
        // 取消事件拦截【移动端】
        document.removeEventListener(
            "touchstart",
            this._touchstart,
            false
        );
        document.removeEventListener(
            "touchmove",
            this._touchmove,
            false
        );
        document.removeEventListener(
            "touchend",
            this._touchend,
            false
        );
    }
}