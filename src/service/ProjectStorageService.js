export class ProjectStorageService {
    constructor(ctx) {
        this.ctx = ctx;

        this.projectStorageMapper = ctx.projectStorageMapper;
    }

    async save() {
        this.ctx.utils.log(`保存项目...`)
        await this.projectStorageMapper.save(this.ctx.data.project.id, this.ctx.data.project.name, this._convertProjectToData());
        this.ctx.utils.outputDevTip('保存项目成功');
    }

    async load(opts) {
        const project = await this.ctx.projectStorageMapper.load(this.ctx.data.project.id);
        if (project) {
            opts.beforeMainStartItems = project.data.beforeMainStartItems;
            opts.afterMainStartItems = project.data.afterMainStartItems;
            opts.nodes = project.data.nodes;
            return true;
        }
        return false;
    }

    /**
     * 序列化项目数据
     * @returns {{beforeMainStartItems: [{type: string, onlyDevViewport: boolean, data: {canExposeContext: boolean, showFPS: boolean, showMemory: boolean, activeSceneId: *}},{type: string, onlyDevViewport: boolean, data: *},{type: string, onlyViewport: boolean, data: {showFPS: boolean, showMemory: boolean}},{type: string, data: {unitSize: *, gravityUnit: *}},{type: string, data: {startPosUnit: *, startLookAtPosUnit: *, heightUnit: *, moveSpeedUnit: *, fastMoveSpeedUnit: *, jumpStrengthUnit: *, showCCI: *}}], afterMainStartItems: *[]}}
     * @private
     */
    _convertProjectToData() {
        const { ctx } = this;

        const data = {
            beforeMainStartItems: [
                /*** 设计师配置 ***/
                {
                    type: 'ctx-init',
                    onlyDevViewport: true,
                    data: {
                        // canExposeContext: true,
                        showFPS: true,
                        showMemory: true,
                        activeSceneId: ctx.data.scene.id,
                    }
                },
                {
                    type: 'camera-init',
                    onlyDevViewport: true,
                    data: ctx.data.camera,
                },
                /*** 玩家配置 ***/
                {
                    type: 'ctx-init',
                    onlyViewport: true,
                    data: {
                        showFPS: true,
                        showMemory: true,
                        activeSceneId: ctx.data.scene.id,
                    }
                },
                /*** 通用配置 ***/
                {
                    type: 'world-init',
                    data: {
                        unitSize: ctx.data.world.unitSize,
                        gravityUnit: ctx.data.world.gravityUnit,
                    }
                },
                {
                    type: 'player-init',
                    data: {
                        // 初始位置
                        startPosUnit: ctx.data.player.startPosUnit,
                        // 初始朝向点
                        startLookAtPosUnit: ctx.data.player.startLookAtPosUnit,
                        // 玩家身高，1.75米
                        heightUnit: ctx.data.player.heightUnit,
                        moveSpeedUnit: ctx.data.player.moveSpeedUnit,
                        fastMoveSpeedUnit: ctx.data.player.fastMoveSpeedUnit,
                        jumpStrengthUnit: ctx.data.player.jumpStrengthUnit,

                        showCCI: ctx.data.player.showCCI,
                    }
                },
            ],
            afterMainStartItems: [
                // /*** 设计师配置 ***/
                // // 创建世界网格
                // {
                //     type: 'world-grid-build',
                //     onlyDevViewport: true,
                //     data: {
                //         __autoSave: false,
                //         eachGridSizeUnit: 1,
                //         totalGridCount: 51,
                //         colorCenterLine: 'blue',
                //         colorGrid: 'silver',
                //         pos: [0, 0.5, 0],
                //     }
                // },
                // // 创建中心坐标指示器
                // {
                //     type: 'object-build',
                //     onlyDevViewport: (this.ctx.data.player.showCenterIndicator ? null : true),
                //     data: {
                //         __autoSave: false,
                //         helper: {
                //             type: 'center-axes-indicator'
                //         }
                //     }
                // },
                // // 创建第一人称视角控制能力（设计师）
                // {
                //     type: 'FirstPersonControlsAbility-ability-build',
                //     onlyDevViewport: true,
                //     name: 'first_person_controls',
                //     data: {
                //         __autoSave: false,
                //         gravity: 0,
                //         collisionEnabled: false,
                //         upDownEnabled: true,
                //     }
                // },
                // // 创建选中游戏对象能力
                // {
                //     type: 'SelectCGObjectAbility-ability-build',
                //     onlyDevViewport: true,
                //     name: 'select-cgo-dev',
                //     data: {
                //         __autoSave: false,
                //         singleSelectMode: true,
                //     }
                // },
                // // 创建移动选中对象能力
                // {
                //     type: 'MoveSelectedCGObjectAbility-ability-build',
                //     onlyDevViewport: true,
                //     name: 'move-selected-cgo-dev',
                //     data: {
                //         __autoSave: false,
                //     }
                // },
                // /*** 玩家配置 ***/
                // // 创建第一人称视角控制能力（玩家）
                // {
                //     type: 'FirstPersonControlsAbility-ability-build',
                //     onlyViewport: true,
                //     name: 'first_person_controls',
                //     data: {
                //         __autoSave: false,
                //     }
                // },
                // /*** 通用配置 ***/
                // // 创建天空
                // {
                //     type: 'sky-object-build',
                //     data: {
                //         __autoSave: false,
                //     }
                // },
                // // 创建光源
                // {
                //     type: 'light-object-build',
                //     data: {
                //         __autoSave: false,
                //         ambientLight: {
                //         }
                //     }
                // },
                // // 创建用户控制能力
                // {
                //     type: 'UserControlsAbility-ability-build',
                //     name: 'user_ctrl',
                //     data: {
                //         __autoSave: false,
                //     }
                // },
                // // 创建获取用户变动事件能力
                // {
                //     type: 'GetUserChangeAnyEventAbility-ability-build',
                //     name: 'get_user_change_any_event',
                //     data: {
                //         __autoSave: false,
                //     }
                // },
            ],
            nodes: this.ctx.cgWorld.getNodeContainer().toData(),
        };

        // // 生成动态添加的游戏对象
        // const cgoList = this.ctx.cgWorld.getObjects();
        // for (const cgo of cgoList) {
        //     if (cgo.data.__autoSave !== false) {
        //         data.afterMainStartItems.push({
        //             type: `${cgo.constructor.name}-object-build`,
        //             data: cgo.data,
        //         })
        //     }
        // }

        return data;
    }
}