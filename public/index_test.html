<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title></title>
    <meta name='viewport'
          content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no'/>
    <link href="" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }

        #gameCanvas {
            border: 1px solid #fff;
            background-color: #333;
        }
    </style>
    <script src="./src/main-start.js"></script>
</head>

<body>
<div id="container"></div>
<script type="importmap">
    {
        "imports": {
            "three": "./lib/threejs/build/three.module.js",
            "three/webgpu": "./lib/threejs/build/three.webgpu.js",
            "three/tsl": "./lib/threejs/build/three.tsl.js",
            "three/addons/CSS2DRenderer": "./lib/threejs/examples/jsm/renderers/CSS2DRenderer.js",
            "three/addons/FBXLoader": "./lib/threejs/examples/jsm/loaders/FBXLoader.js",
            "three/addons/OrbitControls": "./lib/threejs/examples/jsm/controls/OrbitControls.js",
            "three/addons/Sky": "./lib/threejs/examples/jsm/objects/Sky.js",
            "cannon-es": "./lib/cannon-es/cannon-es.js",
            "cannon-es-debugger": "./lib/cannon-es/cannon-es-debugger.js"
        }
    }
</script>
<script type="module">
    import * as THREE from 'three';
    import {OrbitControls} from 'three/addons/OrbitControls';

    // --- 场景 & 渲染器 ---
    const scene = new THREE.Scene();
    const renderer = new THREE.WebGLRenderer({antialias: true});
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);

    // --- 主相机 ---
    const mainCamera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 100);
    mainCamera.position.set(5, 5, 5);
    mainCamera.lookAt(0, 0, 0);

    // --- 控制器 ---
    const controls = new OrbitControls(mainCamera, renderer.domElement);

    // --- 相机组（相机 + 助手 + 图标）---
    function createCameraRig(name, color, position) {
        const rig = new THREE.Group();

        // 目标相机
        const cam = new THREE.PerspectiveCamera(45, 1, 0.1, 5);
        cam.position.copy(position);
        cam.lookAt(0, 0, 0);
        rig.add(cam);

        // 相机助手
        const helper = new THREE.CameraHelper(cam);
        helper.material.color.set(color);
        rig.add(helper);

        // 相机图标（Sprite）
        const textureLoader = new THREE.TextureLoader();
        const iconTexture = textureLoader.load("https://cdn-icons-png.flaticon.com/512/747/747562.png");
        const spriteMaterial = new THREE.SpriteMaterial({map: iconTexture, depthTest: false});
        const icon = new THREE.Sprite(spriteMaterial);
        icon.scale.set(0.5, 0.5, 0.5);
        icon.position.set(0, 0.7, 0); // 放在相机上方一点
        cam.add(icon);

        // 给 group 起名字（方便调试）
        rig.name = name;

        // 额外保存引用，方便外面 update()
        rig.userData = {camera: cam, helper: helper};

        return rig;
    }

    // 创建两个相机组
    const rig1 = createCameraRig("MainCam", 0xff0000, new THREE.Vector3(2, 2, 2));
    const rig2 = createCameraRig("DebugCam", 0x00ff00, new THREE.Vector3(-2, 2, -2));

    scene.add(rig1);
    scene.add(rig2);

    // --- 灯光 & 坐标辅助 ---
    scene.add(new THREE.AmbientLight(0xffffff, 0.5));
    scene.add(new THREE.DirectionalLight(0xffffff, 1));
    scene.add(new THREE.AxesHelper(5));

    // --- 动画循环 ---
    function animate() {
        requestAnimationFrame(animate);
        controls.update();

        // 更新每个相机助手
        scene.traverse(obj => {
            if (obj.type === "Group" && obj.userData.helper) {
                obj.userData.helper.update();
            }
        });

        renderer.render(scene, mainCamera);
    }

    animate();

    // --- 窗口自适应 ---
    window.addEventListener('resize', () => {
        mainCamera.aspect = window.innerWidth / window.innerHeight;
        mainCamera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
</script>
</body>

</html>