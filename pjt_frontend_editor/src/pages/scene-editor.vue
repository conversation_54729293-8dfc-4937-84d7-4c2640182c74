<template>
  <div class="scene-editor">
    <!-- 顶栏 -->
    <div class="layer0-top-area">

    </div>
    <div class="layer0-main-area">
      <!-- 左栏 -->
      <div class="layer1-left-area">
        <el-tabs type="border-card">
          <el-tab-pane label="层级管理器">
            <node-bar ref="nodeBar0"></node-bar>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div style="width: 10px;height: 100%;"></div>
      <!-- 沙盒视图（游戏引擎渲染区域） -->
      <div class="layer1-center-area">
        <iframe ref="sandbox0" :src="getSceneViewUrl()"></iframe>
      </div>
      <div style="width: 10px;height: 100%;"></div>
      <!-- 右栏 -->
      <div class="layer1-right-area">
        <el-tabs type="border-card">
          <el-tab-pane label="属性检查器"></el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import nodeBar from "@/pages/mixin/scene-editor/node-bar.js";
import NodeBar from "@/pages/components/scene-editor/node-bar.vue";
import {waitFuncDone, waitGetObj} from "@/code/util/code-util.js";
import appConfig from "@/config/app-config.js";

/**
 * 场景编辑器
 */
export default {
  name: 'scene-editor',
  components: {NodeBar},
  mixins: [
      nodeBar
  ],
  data() {
    return {
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      const self = this;
      const ctx = this.getCtx();

      console.log('项目ID：', this.$route.params.projectId);

      // 等待沙盒准备完毕
      const sandbox0 = await waitGetObj(this.$refs, 'sandbox0');
      const sandboxConnection = await waitGetObj(sandbox0.contentWindow, 'sandboxConnection');
      ctx.sandboxConnection = sandboxConnection;
      await waitFuncDone(() => {
        return sandboxConnection.isReady() && sandboxConnection.isLoadProjectDataDone();
      })
      console.log('沙盒准备完毕！')

      // 初始化
      const initResult = await sandboxConnection.init({
        projectId: this.$route.params.projectId,
        projectName: `未命名的项目（${this.$route.params.projectId}）`,
        showFPS: true,
        showMemory: true,
        player: {
          moveSpeedUnit: 2,
          fastMoveSpeedUnit: 6,
        }
      });
      // // 开启天空盒子
      // sandboxConnection.openSkyBox({});
      // 开启相机控制
      sandboxConnection.openCameraControls({
        gravity: 0,
        collisionEnabled: false,
        upDownEnabled: true,
        useRightMouseDown: true,
      });
      // 开启开发者网格
      sandboxConnection.openGrid({
        eachGridSizeUnit: 1,
        totalGridCount: 51,
        colorCenterLine: 'blue',
        colorGrid: 'silver',
        pos: [0, 0, 0],
      });
      // 开启中心坐标指示器
      sandboxConnection.openCenterAxesIndicator({
      });
      // 添加开发者提示事件监听
      sandboxConnection.addDevTipEventListener((msg) => {
        self.$message({ message: msg, type: 'success' });
      });
      // 添加节点容器变化事件监听
      sandboxConnection.addNodeContainerChangeEventListener((pars) => {
        // 从沙盒同步节点
        self.syncNodesFromSandbox();
      });
      // 构建
      sandboxConnection.build();

      // 从沙盒同步节点
      this.syncNodesFromSandbox();
    },
    getSceneViewUrl() {
      // 本地使用代理调试
      if (location.href.indexOf('localhost:5000') !== -1) {
        return `http://localhost:5000/sandbox/index_3d_v2_editor.html?pjt=${this.$route.params.projectId}`;
      }
      return `${appConfig.backRootUrl}index_3d_v2_editor.html?pjt=${this.$route.params.projectId}`;
    },
    /**
     * 从沙盒同步节点
     */
    syncNodesFromSandbox() {
      const ctx = this.getCtx();
      this.$refs.nodeBar0.setData(ctx.sandboxConnection.getCtx().cgWorld.getNodeContainer().toData())
    }
  },
  async mounted() {
    await this.onShowed();
  }
}
</script>

<style scoped lang="less">
.scene-editor {
  display: flex;
  flex-direction: column;
  height: 100%;

  .layer0-top-area {
    height: 60px;
  }

  .layer0-main-area {
    flex-grow: 1;
    display: flex;
    flex-direction: row;

    .layer1-left-area {
      width: 300px;
    }

    .layer1-center-area {
      flex-grow: 1;

      iframe {
        width: 100%;
        height: 100%;
        border: 0;
      }
    }

    .layer1-right-area {
      width: 300px;
    }
  }
}
</style>